# -*- coding: utf-8 -*-
import hashlib
import hmac
import json
import requests
import uuid
import platform
import psutil
import logging
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, AccessError
from odoo.tools import config

_logger = logging.getLogger(__name__)

class SystemLicenseManager(models.Model):
    """Handles license validation and system authentication"""
    _name = 'system.license.manager'
    _description = 'System License Manager'
    _auto = False  # This is a utility model, no database table needed

    @api.model
    def _get_server_fingerprint(self):
        """Generate unique server fingerprint for license binding"""
        try:
            # Get system information
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                  for elements in range(0,2*6,2)][::-1])
            hostname = platform.node()
            cpu_info = platform.processor()
            
            # Create fingerprint
            fingerprint_data = f"{mac_address}:{hostname}:{cpu_info}"
            return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
        except Exception as e:
            _logger.warning(f"Could not generate server fingerprint: {e}")
            return "DEFAULT_FINGERPRINT"

    @api.model
    def _get_installation_id(self):
        """Get or create unique installation ID"""
        ICP = self.env['ir.config_parameter'].sudo()
        installation_id = ICP.get_param('system.installation_id')
        
        if not installation_id:
            installation_id = str(uuid.uuid4())
            ICP.set_param('system.installation_id', installation_id)
            
        return installation_id

    @api.model
    def _validate_license_server(self, license_key, module_name):
        """Validate license with remote server"""
        try:
            # Your license server URL
            LICENSE_SERVER = "https://license.yourcompany.com/api/validate"
            
            payload = {
                'license_key': license_key,
                'hardware_id': self._get_server_fingerprint(),
                'installation_id': self._get_installation_id(),
                'module_name': module_name,
                'odoo_version': '16.0',
                'timestamp': datetime.now().isoformat()
            }
            
            response = requests.post(LICENSE_SERVER, json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('valid'):
                    # Cache the validation result
                    self._cache_license_validation(license_key, result)
                    return result
                    
            return {'valid': False, 'error': 'Invalid license'}
            
        except requests.RequestException as e:
            _logger.warning(f"License server unreachable: {e}")
            # Fall back to cached validation
            return self._get_cached_license_validation(license_key)
        except Exception as e:
            _logger.error(f"License validation error: {e}")
            return {'valid': False, 'error': 'Validation failed'}

    @api.model
    def _cache_license_validation(self, license_key, validation_result):
        """Cache license validation result"""
        ICP = self.env['ir.config_parameter'].sudo()
        cache_key = f"license.cache.{hashlib.md5(license_key.encode()).hexdigest()}"
        
        cache_data = {
            'result': validation_result,
            'cached_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        ICP.set_param(cache_key, json.dumps(cache_data))

    @api.model
    def _get_cached_license_validation(self, license_key):
        """Get cached license validation if available and not expired"""
        try:
            ICP = self.env['ir.config_parameter'].sudo()
            cache_key = f"license.cache.{hashlib.md5(license_key.encode()).hexdigest()}"
            cached_data = ICP.get_param(cache_key)
            
            if cached_data:
                cache_info = json.loads(cached_data)
                expires_at = datetime.fromisoformat(cache_info['expires_at'])
                
                if datetime.now() < expires_at:
                    return cache_info['result']
                    
            return {'valid': False, 'error': 'No valid cache'}
            
        except Exception as e:
            _logger.warning(f"Cache validation error: {e}")
            return {'valid': False, 'error': 'Cache error'}

    @api.model
    def _is_operational_mode_allowed(self, module_name='travel_agent_management'):
        """Main license check - disguised as operational mode check"""
        try:
            ICP = self.env['ir.config_parameter'].sudo()
            license_key = ICP.get_param(f'{module_name}.license_key')
            
            if not license_key:
                return False
                
            validation = self._validate_license_server(license_key, module_name)
            return validation.get('valid', False)
            
        except Exception as e:
            _logger.error(f"Operational mode check failed: {e}")
            return False

    @api.model
    def _meets_compliance_standards(self, feature_name=None):
        """Another disguised license check for specific features"""
        if not self._is_operational_mode_allowed():
            return False
            
        if feature_name:
            # Check if specific feature is licensed
            ICP = self.env['ir.config_parameter'].sudo()
            licensed_features = ICP.get_param('system.licensed_features', '[]')
            try:
                features = json.loads(licensed_features)
                return feature_name in features
            except:
                return False
                
        return True

    @api.model
    def _apply_performance_throttling(self):
        """Slow down unlicensed systems"""
        if not self._is_operational_mode_allowed():
            import time
            time.sleep(0.5)  # Add delay for unlicensed systems

    @api.model
    def install_license(self, license_key):
        """Install a new license key"""
        validation = self._validate_license_server(license_key, 'travel_agent_management')
        
        if validation.get('valid'):
            ICP = self.env['ir.config_parameter'].sudo()
            ICP.set_param('travel_agent_management.license_key', license_key)
            
            # Store licensed features
            features = validation.get('features', [])
            ICP.set_param('system.licensed_features', json.dumps(features))
            
            return True
        else:
            raise UserError(_("Invalid license key: %s") % validation.get('error', 'Unknown error'))

class ResCompany(models.Model):
    """Add license-related methods to company"""
    _inherit = 'res.company'

    def _is_operational_mode_allowed(self):
        """Company-level operational check"""
        return self.env['system.license.manager']._is_operational_mode_allowed()

    def _meets_compliance_standards(self, feature=None):
        """Company-level compliance check"""
        return self.env['system.license.manager']._meets_compliance_standards(feature)

    def _apply_rate_limits(self):
        """Apply performance throttling if needed"""
        self.env['system.license.manager']._apply_performance_throttling()
